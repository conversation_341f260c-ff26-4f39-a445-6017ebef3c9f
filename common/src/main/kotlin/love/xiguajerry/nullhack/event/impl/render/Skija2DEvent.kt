package love.xiguajerry.nullhack.event.impl.render

//import io.github.humbleui.skija.Canvas
//import io.github.humbleui.skija.DirectContext
//import io.github.humbleui.skija.Surface
//import love.xiguajerry.nullhack.event.api.EventBus
//import love.xiguajerry.nullhack.event.api.IEvent
//import love.xiguajerry.nullhack.event.api.IPosting
//import love.xiguajerry.nullhack.event.impl.render.Render2DEvent
//import net.minecraft.client.gui.DrawContext
//
//class Skija2DEvent(val context: DirectContext, val surface: Surface, val canvas: Canvas) : IEvent, IPosting by Companion {
//    companion object : EventBus()
//}