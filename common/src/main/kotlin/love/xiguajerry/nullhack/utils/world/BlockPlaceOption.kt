/*
 * Copyright (c) 2021-2022, SagiriXiguajerry. All rights reserved.
 * This repository will be transformed to SuperMic_233.
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */
package love.xiguajerry.nullhack.utils.world

class BlockPlaceOption(
    val isEntityCollisionIgnored: <PERSON><PERSON>an,
    val isBlockCollisionIgnored: <PERSON><PERSON>an,
    val isAirPlaceIgnored: Boolean
) {

    constructor(vararg options: PlaceOptions) : this(
        contains(options, PlaceOptions.IGNORE_ENTITY),
        contains(options, PlaceOptions.IGNORE_BLOCK),
        contains(options, PlaceOptions.IGNORE_AIR)
    )

    companion object {
        private fun contains(options: Array<out PlaceOptions>, any: PlaceOptions): <PERSON><PERSON><PERSON> {
            return any in options
        }
    }
}