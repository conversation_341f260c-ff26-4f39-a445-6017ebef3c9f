package love.xiguajerry.nullhack.utils.collections

import love.xiguajerry.nullhack.utils.Alias
import java.util.concurrent.ConcurrentHashMap

class AliasSet<T : Alias>(
    map: MutableMap<String, T> = ConcurrentHashMap()
) : NameableSet<T>(map) {
    override fun add(element: T): <PERSON><PERSON><PERSON> {
        var modified = super.add(element)
        element.alias.forEach { alias ->
            val prevValue = map.put(alias.toString().lowercase(), element)
            if (prevValue != null && prevValue != element) {
                remove(prevValue)
            }
            modified = prevValue != element || modified
        }
        return modified
    }

    override fun remove(element: T): <PERSON><PERSON>an {
        var modified = super.remove(element)
        element.alias.forEach {
            modified = map.remove(it.toString().lowercase()) != null || modified
        }
        return modified
    }
}
