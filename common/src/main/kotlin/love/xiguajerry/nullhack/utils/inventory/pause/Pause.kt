package love.xiguajerry.nullhack.utils.inventory.pause

import love.xiguajerry.nullhack.modules.AbstractModule

interface IPause {
    fun requestPause(module: AbstractModule): <PERSON><PERSON>an
}

interface ITimeoutPause : IPause {
    override fun requestPause(module: AbstractModule): <PERSON><PERSON><PERSON> {
        return requestPause(module, 50L)
    }

    fun requestPause(module: AbstractModule, timeout: Int): <PERSON><PERSON><PERSON> {
        return requestPause(module, timeout.toLong())
    }

    fun requestPause(module: AbstractModule, timeout: Long): <PERSON><PERSON><PERSON>
}