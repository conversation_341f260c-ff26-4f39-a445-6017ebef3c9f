public    com.mojang.blaze3d.platform.Window framebufferWidth
public    com.mojang.blaze3d.platform.Window framebufferHeight
public    net.minecraft.client.renderer.GameRenderer renderBuffers
public    com.mojang.blaze3d.platform.GlStateManager TEXTURES
public    com.mojang.blaze3d.platform.GlStateManager$TextureState
public    com.mojang.blaze3d.platform.GlStateManager$TextureState binding
public    net.minecraft.client.multiplayer.ClientChunkCache$Storage
public    net.minecraft.client.Options touchscreen
public-f  net.minecraft.client.gui.components.AbstractSelectionList getEntryAtPosition(DD)Lnet/minecraft/client/gui/components/AbstractSelectionList$Entry;
public    net.minecraft.client.multiplayer.ClientLevel getEntities()Lnet/minecraft/world/level/entity/LevelEntityGetter;
public    net.minecraft.client.multiplayer.ClientLevel players
public    net.minecraft.client.player.LocalPlayer usingItemHand
public    net.minecraft.client.multiplayer.ClientLevel blockStatePredictionHandler
public    net.minecraft.client.multiplayer.prediction.BlockStatePredictionHandler currentSequenceNr
public    net.minecraft.network.protocol.game.ServerboundInteractPacket$ActionType
public    net.minecraft.world.inventory.HorseInventoryMenu horse
public    net.minecraft.client.gui.screens.inventory.CreativeModeInventoryScreen selectedTab
public    net.minecraft.world.item.CreativeModeTabs INVENTORY
public    net.minecraft.client.multiplayer.MultiPlayerGameMode ensureHasSentCarriedItem()V
public    net.minecraft.client.multiplayer.MultiPlayerGameMode startPrediction
public    net.minecraft.world.item.Item components
public    net.minecraft.network.protocol.game.ServerboundInteractPacket ATTACK_ACTION
public    net.minecraft.network.protocol.game.ServerboundInteractPacket <init>(IZLnet/minecraft/network/protocol/game/ServerboundInteractPacket$Action;)V
public    net.minecraft.world.level.block.state.BlockBehaviour hasCollision
public    net.minecraft.network.protocol.game.ServerboundInteractPacket write(Lnet/minecraft/network/FriendlyByteBuf;)V
public    net.minecraft.world.entity.LivingEntity attackStrengthTicker
public    net.minecraft.network.protocol.game.ServerboundInteractPacket$Action
public    net.minecraft.network.protocol.game.ServerboundInteractPacket action
public    net.minecraft.network.protocol.game.ServerboundInteractPacket$Action getType()Lnet/minecraft/network/protocol/game/ServerboundInteractPacket$ActionType;
public    net.minecraft.network.protocol.game.ServerboundInteractPacket entityId
public    net.minecraft.world.damagesource.DamageSource causingEntity
public    net.minecraft.client.User uuid
public    net.minecraft.client.multiplayer.ClientLevel playSound(DDDLnet/minecraft/sounds/SoundEvent;Lnet/minecraft/sounds/SoundSource;FFZJ)V
public-f  net.minecraft.world.entity.PositionMoveRotation xRot
public-f  net.minecraft.world.entity.PositionMoveRotation yRot
public    net.minecraft.world.level.chunk.PalettedContainer data
public    net.minecraft.world.level.chunk.PalettedContainer$Data
public    net.minecraft.world.level.chunk.PalettedContainer$Data storage
public    net.minecraft.world.level.chunk.PalettedContainer$Data palette
public    net.minecraft.world.entity.Entity setSharedFlag(IZ)V
public    net.minecraft.client.KeyMapping CATEGORY_SORT_ORDER
public    net.minecraft.client.multiplayer.MultiPlayerGameMode startPrediction(Lnet/minecraft/client/multiplayer/ClientLevel;Lnet/minecraft/client/multiplayer/prediction/PredictiveAction;)V
public    net.minecraft.client.Minecraft metricsRecorder