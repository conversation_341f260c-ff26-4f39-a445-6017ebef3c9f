{"required": true, "minVersion": "0.8", "package": "love.xiguajerry.nullhack.mixins", "compatibilityLevel": "JAVA_21", "refmap": "${mod_id}.refmap.json", "priority": 2100000000, "plugin": "love.xiguajerry.nullhack.MixinPlugin", "client": ["MixinKeyboard", "MixinMinecraft", "MixinOptions", "accessor.IAdvancementWidgetAccessor", "accessor.IBookScreenAccessor", "accessor.IClientChunkManagerAccessor", "accessor.IClientChunkMapAccessor", "accessor.IClientLevelAccessor", "accessor.IMinecraftClientAccessor", "gui.screen.MixinAdvancementsScreen", "gui.screen.MixinAdvancementTab", "gui.screen.MixinClientLanguage", "gui.screen.MixinRecipeBookWidget", "language.MixinLanguageOptionsScreen", "language.MixinLanguageManager", "network.MixinClientPacketListener", "player.MixinKeyboardInput", "player.MixinLocalPlayer", "player.MixinMultiPlayerGameMode", "remove_poll.MixinRenderSystem", "render.MixinArmorFeatureRenderer", "render.MixinArmorStandEntityRenderer", "render.MixinBackgroundRenderer", "render.MixinCamera", "render.MixinEntityRenderDispatcher", "<PERSON>.MixinEntity<PERSON><PERSON>er", "render.MixinGame<PERSON><PERSON>er", "render.MixinInactivityFpsLimiter", "render.MixinIngameHud", "render.MixinInGameOverlayRenderer", "render.MixinItemFrameEntityRenderer", "render.MixinLightTexture", "render.MixinLivingEntityRenderer", "render.MixinMobEntityRenderer", "render.MixinParticleEngine", "render.MixinVertexBuffer", "<PERSON>.MixinWindow", "render.MixinWorld<PERSON><PERSON>er", "world.MixinClientLevel"], "server": [], "injectors": {"defaultRequire": 1}, "mixins": ["accessor.IClientboundSectionBlocksUpdatePacketAccessor", "accessor.IEntityVelocityUpdateS2CPacketAccessor", "accessor.IPlayerMoveC2SPacketAccessor", "accessor.IPositionMoveRotationAccessor", "accessor.IServerboundHelloPacketAccessor", "accessor.ISignTextAccessor", "accessor.ITextDisplayEntityAccessor", "entity.MixinEntity", "entity.MixinLivingEntity", "gui.screen.MixinTranslatableTextContent", "language.MixinLanguage", "network.MixinClientConnection", "option.MixinKeyBinding", "player.MixinPlayer", "world.MixinBlock", "world.MixinBlockItem", "world.MixinBlockState", "world.MixinCobwebBlock", "world.MixinLevel"]}