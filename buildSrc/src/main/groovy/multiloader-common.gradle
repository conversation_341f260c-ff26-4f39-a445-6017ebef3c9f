plugins {
    id 'java-library'
    id 'maven-publish'
}

base {
    archivesName = "${mod_id}-${project.name}-${minecraft_version}"
}

java {
    toolchain.languageVersion = JavaLanguageVersion.of(java_version)
    withSourcesJar()
    withJavadocJar()
}

repositories {
    mavenCentral()
    // https://docs.gradle.org/current/userguide/declaring_repositories.html#declaring_content_exclusively_found_in_one_repository
    exclusiveContent {
        forRepository {
            maven {
                name = 'Sponge'
                url = 'https://repo.spongepowered.org/repository/maven-public'
            }
        }
        filter { includeGroupAndSubgroups('org.spongepowered') }
    }
    exclusiveContent {
        forRepositories(
                maven {
                    name = 'ParchmentMC'
                    url = 'https://maven.parchmentmc.org/'
                },
                maven {
                    name = "NeoForge"
                    url = 'https://maven.neoforged.net/releases'
                }
        )
        filter { includeGroup('org.parchmentmc.data') }
    }
    maven {
        name = 'BlameJared'
        url = 'https://maven.blamejared.com'
    }
}

// Declare capabilities on the outgoing configurations.
// Read more about capabilities here: https://docs.gradle.org/current/userguide/component_capabilities.html#sec:declaring-additional-capabilities-for-a-local-component
['apiElements', 'runtimeElements', 'sourcesElements', 'javadocElements'].each { variant ->
    configurations."$variant".outgoing {
        capability("$group:${project.name}:$version")
        capability("$group:${base.archivesName.get()}:$version")
        capability("$group:$mod_id-${project.name}-${minecraft_version}:$version")
        capability("$group:$mod_id:$version")
    }
    publishing.publications.configureEach {
        suppressPomMetadataWarningsFor(variant)
    }
}

sourcesJar {
    from(rootProject.file('LICENSE')) {
        rename { "${it}_${mod_name}" }
    }
}

jar {
    from(rootProject.file('LICENSE')) {
        rename { "${it}_${mod_name}" }
    }

    manifest {
        attributes([
                'Specification-Title'   : mod_name,
                'Specification-Vendor'  : mod_author,
                'Specification-Version' : project.jar.archiveVersion,
                'Implementation-Title'  : project.name,
                'Implementation-Version': project.jar.archiveVersion,
                'Implementation-Vendor' : mod_author,
                'Built-On-Minecraft'    : minecraft_version
        ])
    }
}

processResources {
    var expandProps = [
            'version'                      : version,
            'group'                        : project.group, //Else we target the task's group.
            'minecraft_version'            : minecraft_version,
            'minecraft_version_range'      : minecraft_version_range,
            'fabric_version'               : fabric_version,
            'fabric_loader_version'        : fabric_loader_version,
            'mod_name'                     : mod_name,
            'mod_author'                   : mod_author,
            'mod_id'                       : mod_id,
            'license'                      : license,
            'description'                  : project.description,
            'neoforge_version'             : neoforge_version,
            'neoforge_loader_version_range': neoforge_loader_version_range,
            "forge_version"                : forge_version,
            "forge_loader_version_range"   : forge_loader_version_range,
            'credits'                      : credits,
            'java_version'                 : java_version
    ]

    var jsonExpandProps = expandProps.collectEntries {
        key, value -> [(key): value instanceof String ? value.replace("\n", "\\\\n") : value]
    }

    filesMatching(['META-INF/mods.toml', 'META-INF/neoforge.mods.toml']) {
        expand expandProps
    }

    filesMatching(['pack.mcmeta', 'fabric.mod.json', '*.mixins.json']) {
        expand jsonExpandProps
    }

    inputs.properties(expandProps)
}

publishing {
    publications {
        register('mavenJava', MavenPublication) {
            artifactId base.archivesName.get()
            from components.java
        }
    }
    repositories {
        maven {
            url System.getenv('local_maven_url')
        }
    }
}
